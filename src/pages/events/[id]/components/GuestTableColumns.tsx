import React from "react";
import { Stack, Typography, Chip } from "@mui/material";
import { Guest } from "src/types";
import { GuestStatusIndicator } from "src/pages/meetings";
import { FollowUpTemplateDropdown } from "src/componentsV2/FollowUpTemplateDropdown";
import { HostSelectInput } from "src/componentsV2/inputs";
import { SkinnyMeetingDefinition } from "src/queries";

interface GuestTableColumnsProps {
  editingTemplateGuestId: number | null;
  editingHostGuestId: number | null;
  refreshTrigger: number;
  onTemplateAssignment: (guestId: number, template: SkinnyMeetingDefinition | null) => void;
  onCreateNewTemplate: () => void;
  onEditTemplate: (guestId: number) => void;
  onAssignHost: (guestId: number, hostId: number) => void;
  onEditHost: (guestId: number) => void;
}

export function createGuestTableColumns({
  editingTemplateGuestId,
  editingHostGuestId,
  refreshTrigger,
  onTemplateAssignment,
  onCreateNewTemplate,
  onEditTemplate,
  onAssignHost,
  onEditHost,
}: GuestTableColumnsProps) {
  return [
    {
      id: "guestInfo",
      label: "Guest",
      component: (row: Guest) => (
        <Stack>
          <Typography variant="caption">
            {row.firstName} {row.lastName}
          </Typography>
          <Typography variant="caption">
            {row.email}
          </Typography>
        </Stack>
      ),
    },
    {
      id: "status",
      label: "Guest Status",
      component: (row: Guest) => (
        <GuestStatusIndicator status={row.status} />
      ),
    },
    {
      id: "followUpTemplate",
      label: "Follow Up Template",
      component: (row: Guest) => (
        <Stack sx={{ minWidth: 200 }}>
          {editingTemplateGuestId === row.id ? (
            <FollowUpTemplateDropdown
              value={row.followUpTemplate as SkinnyMeetingDefinition}
              onChange={(template) => onTemplateAssignment(row.id, template)}
              onCreateNewTemplate={onCreateNewTemplate}
              refreshTrigger={refreshTrigger}
            />
          ) : (
            <Stack
              onClick={() => onEditTemplate(row.id)}
              sx={{
                cursor: "pointer",
                "&:hover": { backgroundColor: "action.hover" },
                p: 1,
                borderRadius: 1
              }}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  onEditTemplate(row.id);
                }
              }}
            >
              {row.followUpTemplate ? (
                <>
                  <Typography
                    variant="caption"
                    fontWeight="bold"
                  >
                    {row.followUpTemplate.name}
                  </Typography>
                  <Chip
                    size="small"
                    label={
                      row.followUpTemplate.inviteStyle ===
                      "calendar_first"
                        ? "Kronologic Invite"
                        : "Kronologic Email"
                    }
                    color={
                      row.followUpTemplate.inviteStyle ===
                      "calendar_first"
                        ? "primary"
                        : "secondary"
                    }
                  />
                </>
              ) : (
                <Typography
                  variant="caption"
                  color="text.secondary"
                >
                  Click to assign template
                </Typography>
              )}
            </Stack>
          )}
        </Stack>
      ),
    },
    {
      id: "assignedHost",
      label: "Assigned Host",
      component: (row: Guest) => (
        <Stack sx={{ minWidth: 150 }}>
          {editingHostGuestId === row.id ? (
            <HostSelectInput
              host={null}
              onHostSelect={(host) => {
                if (host) {
                  onAssignHost(row.id, host.id);
                }
              }}
              forMeetingTransfer={true}
            />
          ) : (
            <Stack
              onClick={() => {
                if (row.followUpTemplate) {
                  onEditHost(row.id);
                }
              }}
              sx={{
                cursor: row.followUpTemplate ? "pointer" : "default",
                "&:hover": row.followUpTemplate ? { backgroundColor: "action.hover" } : {},
                p: 1,
                borderRadius: 1,
                opacity: row.followUpTemplate ? 1 : 0.6,
              }}
              role={row.followUpTemplate ? "button" : undefined}
              tabIndex={row.followUpTemplate ? 0 : undefined}
              onKeyDown={(e) => {
                if (row.followUpTemplate && (e.key === 'Enter' || e.key === ' ')) {
                  e.preventDefault();
                  onEditHost(row.id);
                }
              }}
            >
              <Typography variant="caption">
                {row.assignedHost
                  ? `${row.assignedHost.firstName} ${row.assignedHost.lastName}`
                  : row.followUpTemplate 
                    ? "Click to assign host"
                    : "Template required"}
              </Typography>
            </Stack>
          )}
        </Stack>
      ),
    },
    {
      id: "followUpStatus",
      label: "Follow Up Status",
      component: (row: Guest) => (
        <Chip
          size="small"
          label={
            row.followUpStatus === "ready"
              ? "Ready"
              : row.followUpStatus === "staging"
                ? "Staging"
                : row.followUpStatus === "sent"
                  ? "Sent"
                  : row.followUpStatus === "needs_host"
                    ? "Needs Host"
                    : row.followUpStatus ===
                        "needs_template"
                      ? "Needs Template"
                      : "None"
          }
          color={
            row.followUpStatus === "sent"
              ? "success"
              : row.followUpStatus === "ready"
                ? "info"
                : row.followUpStatus === "staging"
                  ? "warning"
                  : row.followUpStatus === "needs_host" ||
                      row.followUpStatus ===
                        "needs_template"
                    ? "error"
                    : "default"
          }
        />
      ),
    },
  ];
}
