import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Container,
  Grid,
  IconButton,
  Link,
  <PERSON><PERSON>,
  Tab,
  Ta<PERSON>,
  Typography,
  Chip,
} from "@mui/material";
import {
  AccessTime,
  Person,
  Delete,
  PlayArrow,
  PersonAdd, CalendarMonth, MailOutline,
} from '@mui/icons-material';
import { Link as RouterLink, useParams, useHistory } from "react-router-dom";
import Breadcrumbs from "@mui/material/Breadcrumbs";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import dayjs from "dayjs";

import { useEventDetails } from "src/queries/useEventDetails";
import Loading from "src/componentsV2/Loading";
import ErrorPage from "src/pages/ErrorPage";
import { GuestStatusIndicator } from "src/pages/meetings";
import { SelectTable } from "src/componentsV2/tables/SelectTable";
import { useWebinarGuestCount } from "src/queries/useWebinarGuestCount";
import {
  useAddEventGuest,
  useUpdateMeetings,
  useDeleteWebinars,
  useAssignFollowUpTemplate,
  useBeginFollowUp,
  useAssignHost,
} from "src/mutations";
import { useSnackbar, useConfirmationDialog } from "src/hooks";
import { Contact, GuestStatus, Guest } from "src/types";
import { mutate } from "swr";
import { AddEventGuestDialog } from "src/componentsV2/dialogs/AddEventGuestDialog";
import { FollowUpButton } from "src/componentsV2/FollowUpButton";
import { FollowUpTemplateDropdown } from "src/componentsV2/FollowUpTemplateDropdown";
import { CreateTemplateModal } from "src/componentsV2/dialogs/CreateTemplateModal";
import { HostSelectInput } from "src/componentsV2/inputs";
import { SkinnyMeetingDefinition } from "src/queries";
import { INVITE_STYLE } from '../../../meetingTypes/invite/props';

type EventDetailsPageTabs =
  | "Everyone"
  | "Accepted"
  | "Declined"
  | "No Response"
  | "Awaiting Response";

const TABS = [
  { label: 'Everyone', value: 'Everyone' as EventDetailsPageTabs, filter: '' as GuestStatus | '' },
  { label: 'Accepted', value: 'Accepted' as EventDetailsPageTabs, filter: 'accepted' as GuestStatus },
  { label: 'Declined', value: 'Declined' as EventDetailsPageTabs, filter: 'declined' as GuestStatus },
  { label: 'No Response', value: 'No Response' as EventDetailsPageTabs, filter: 'no_response' as GuestStatus },
  { label: 'Awaiting Response', value: 'Awaiting Response' as EventDetailsPageTabs, filter: 'waiting_for_first_response' as GuestStatus },
] as const;

function EventDetails() {
  const { id } = useParams<{ id: string }>();
  const history = useHistory();
  const [openSnackbar] = useSnackbar();
  const [tab, setTab] = useState<EventDetailsPageTabs>("Everyone");
  const [guestStatusFilter, setGuestStatusFilter] = useState<GuestStatus | "">(
    "",
  );
  const openConfirmationDialog = useConfirmationDialog();

  const guestCounts = useWebinarGuestCount(id);

  const { data, loading, error } = useEventDetails(id, {
    guestStatusFilter,
  });

  const [showAddGuestDialog, setShowAddGuestDialog] = useState(false);
  const [selectedGuestIds, setSelectedGuestIds] = useState<(number | string)[]>(
    [],
  );
  const [editingTemplateGuestId, setEditingTemplateGuestId] = useState<
    number | null
  >(null);
  const [showCreateTemplateModal, setShowCreateTemplateModal] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [editingHostGuestId, setEditingHostGuestId] = useState<number | null>(
    null,
  );

  // Get selected guests from the data
  const selectedGuests = (data?.guests || []).filter((guest) =>
    selectedGuestIds.includes(guest.id),
  );

  // Check if selected guests have assigned follow-up templates
  const selectedGuestsWithoutTemplates = selectedGuests.filter(
    (guest) => !guest.followUpTemplate,
  );

  // Check if all guests (not just selected) have templates
  const allGuestsHaveTemplates =
    (data?.guests || []).length > 0 &&
    (data?.guests || []).every((guest) => guest.followUpTemplate);

  // Show "Begin Follow Up" if:
  // 1. All guests have templates (regardless of selection), OR
  // 2. Selected guests all have templates (and some are selected)
  const hasAssignedTemplates =
    allGuestsHaveTemplates ||
    (selectedGuests.length > 0 && selectedGuestsWithoutTemplates.length === 0);

  const updateMeetings = useUpdateMeetings();
  const deleteWebinar = useDeleteWebinars();
  const addEventGuest = useAddEventGuest();
  const assignFollowUpTemplate = useAssignFollowUpTemplate();
  const beginFollowUp = useBeginFollowUp();
  const assignHost = useAssignHost();

  const handleTemplateAssignment = useCallback(
    async (guestId: number, template: SkinnyMeetingDefinition | null) => {
      if (!template) return;

      try {
        await assignFollowUpTemplate({
          eventId: Number.parseInt(id),
          templateId: template.id,
          contactIds: [guestId],
        });
        openSnackbar("Template assigned successfully");
        setEditingTemplateGuestId(null);

        mutate(`/api/events/${id}/details`);
      } catch (error) {
        openSnackbar("Failed to assign template");
      }
    },
    [assignFollowUpTemplate, openSnackbar, id],
  );

  const handleCreateNewTemplate = useCallback(() => {
    setShowCreateTemplateModal(true);
  }, []);

  const handleTemplateCreated = useCallback(
    (newTemplate: SkinnyMeetingDefinition) => {
      setShowCreateTemplateModal(false);
      setRefreshTrigger((prev) => prev + 1); // Trigger dropdown refresh

      // If we were editing a guest, assign the new template to them
      if (editingTemplateGuestId !== null) {
        // Small delay to ensure the template is fully saved before proceeding
        setTimeout(() => {
          handleTemplateAssignment(editingTemplateGuestId, newTemplate);
        }, 100);
      } else {
        setEditingTemplateGuestId(null);
      }
    },
    [editingTemplateGuestId, handleTemplateAssignment],
  );

  // Handle escape key to cancel editing
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        if (editingTemplateGuestId !== null) {
          setEditingTemplateGuestId(null);
        }
        if (editingHostGuestId !== null) {
          setEditingHostGuestId(null);
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [editingTemplateGuestId, editingHostGuestId]);

  const handleAddEventGuest = useCallback(
    async (contact: Contact) => {
      try {
        setShowAddGuestDialog(false);
        await addEventGuest(Number.parseInt(id), contact.id);
        openSnackbar("Event guest successfully added");
      } catch (error: any) {
        const errorMsg =
          error?.response?.body?.message || "Error adding event guest";
        openSnackbar(errorMsg);
      }
    },
    [id, openSnackbar, addEventGuest],
  );

  const activateWebinar = useCallback(async () => {
    const { failures } = await updateMeetings([
      {
        enabled: true,
        id: Number.parseInt(id),
      },
    ]);

    if (failures?.length > 0) {
      openSnackbar("Failed to activate event");
      return;
    }

    await mutate([`/api/webinars/${id}`, { guestStatusFilter }]);

    openSnackbar("Event in Progress");
  }, [id, openSnackbar, updateMeetings, guestStatusFilter]);

  const isActivateDisabled =
    !data || data.status !== "initialized" || data.active === true;

  const isAddGuestDisabled =
    !data || !["initialized", "scheduling"].includes(data.status);

  const handleDeleteWebinar = useCallback(() => {
    openConfirmationDialog(
      "Delete Confirmation",
      "Are you sure you want to delete this webinar event?",
      async () => {
        try {
          await deleteWebinar(Number.parseInt(id));
          history.push("/events");
          openSnackbar("Webinar successfully deleted");
        } catch (error) {
          openSnackbar("Failed to delete webinar");
        }
      },
    );
  }, [id, openSnackbar, deleteWebinar, history, openConfirmationDialog]);

  const isMeetingLink = (s: string) => /^(https?:\/\/|www\.)/.test(s);

  const handleGuestSelection = useCallback(
    (checked: boolean, guests: Guest[]) => {
      if (checked) {
        // Remove guests from selection
        setSelectedGuestIds((prev) =>
          prev.filter((id) => !guests.some((guest) => guest.id === id)),
        );
      } else {
        // Add guests to selection
        setSelectedGuestIds((prev) => [
          ...prev,
          ...guests.map((guest) => guest.id).filter((id) => !prev.includes(id)),
        ]);
      }
    },
    [],
  );

  const handleTemplateAssign = useCallback(
    async (template: SkinnyMeetingDefinition) => {
      try {
        // Only assign template to guests that don't already have one
        const guestsToAssign = selectedGuestsWithoutTemplates;

        if (guestsToAssign.length === 0) {
          openSnackbar("All selected guests already have templates assigned");
          return;
        }

        await assignFollowUpTemplate({
          eventId: Number.parseInt(id),
          templateId: template.id,
          contactIds: guestsToAssign.map((guest) => guest.id),
        });

        openSnackbar(
          `Template "${template.name}" assigned to ${guestsToAssign.length} guest(s)`,
        );

        setSelectedGuestIds([]);

        mutate(`/api/webinars/${id}`);
      } catch (error) {
        openSnackbar("Failed to assign template");
        console.error("Error assigning template:", error);
      }
    },
    [selectedGuestsWithoutTemplates, openSnackbar, assignFollowUpTemplate, id],
  );

  const handleBeginFollowUp = useCallback(async () => {
    try {
      const result = await beginFollowUp({
        eventId: Number.parseInt(id),
      });

      openSnackbar(`Follow-up initiated for ${result.initiated} guest(s)`);
    } catch (error) {
      openSnackbar("Failed to begin follow-up");
      console.error("Error beginning follow-up:", error);
    }
  }, [beginFollowUp, id, openSnackbar]);

  const handleAssignHost = useCallback(
    async (guestId: number, newHostId: number) => {
      try {
        await assignHost({
          eventId: Number.parseInt(id),
          contactId: guestId,
          hostId: newHostId,
        });

        setEditingHostGuestId(null);
        openSnackbar("Host assigned successfully");

        await mutate([`/api/webinars/${id}`, { guestStatusFilter }]);
      } catch (error) {
        openSnackbar("Failed to assign host");
        console.error("Error assigning host:", error);
      }
    },
    [id, openSnackbar, assignHost, guestStatusFilter],
  );

  const getTemplateIcon = useCallback((inviteStyle: string) => {
    switch (inviteStyle) {
      case INVITE_STYLE.CALENDAR_FIRST:
        return <CalendarMonth sx={{ fontSize: 16, mr: 1 }} />;
      case INVITE_STYLE.CUSTOM_INVITE:
        return <MailOutline sx={{ fontSize: 16, mr: 1 }} />;
      default:
        return null;
    }
  }, []);

  if (error) {
    return <ErrorPage status={error.status} />;
  }

  if (loading) {
    return <Loading />;
  }

  if (!data) {
    return <ErrorPage status={404} />;
  }

  return (
    <Container maxWidth="xl" disableGutters sx={{ p: 2 }}>
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="flex-start"
        sx={{ pb: 1 }}
      >
        <Breadcrumbs
          separator={<NavigateNextIcon fontSize="small" />}
          aria-label="breadcrumb"
        >
          <Link component={RouterLink} to="/events">
            Events
          </Link>
          <Typography component="span">{id}</Typography>
        </Breadcrumbs>
        <IconButton
          onClick={handleDeleteWebinar}
          title="Delete Webinar"
          sx={{ color: "text.primary" }}
        >
          <Delete />
        </IconButton>
      </Stack>

      <Stack spacing={2}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignContent="flex-start"
        >
          <Stack spacing={1}>
            {data?.meetingDefinition?.name ? (
              <Typography
                variant="h4"
                sx={{ color: "primary.dark", fontWeight: "bold" }}
              >
                {data?.meetingDefinition?.name}
              </Typography>
            ) : (
              <Typography
                variant="h4"
                sx={{
                  color: "primary.dark",
                  fontWeight: "bold",
                  fontStyle: "italic",
                }}
              >
                [EVENT MISSING]
              </Typography>
            )}

            <Stack direction="row" spacing={2}>
              <Stack direction="row" spacing={1}>
                <Person />
                <Stack>
                  <Typography fontWeight="bold">Host</Typography>
                  {data.host ? (
                    <Stack>
                      <Typography>
                        {data.host.firstName} {data.host.lastName}
                      </Typography>
                      <Typography color="primary">{data.host.email}</Typography>
                    </Stack>
                  ) : (
                    <Typography>No Host</Typography>
                  )}
                </Stack>
              </Stack>

              <Stack direction="row" spacing={1}>
                <AccessTime />
                <Stack>
                  <Typography fontWeight="bold">Meeting Time</Typography>
                  {data.startTime ? (
                    <Stack>
                      <Typography>
                        {dayjs(data.startTime).format("M/D/YYYY")}
                      </Typography>
                      <Typography>
                        {dayjs(data.startTime).format("h:mm A z")}
                      </Typography>
                    </Stack>
                  ) : (
                    <Typography>Not yet scheduled</Typography>
                  )}
                </Stack>
              </Stack>

              <Stack direction="row" spacing={1}>
                <Stack sx={{ width: "100%", maxWidth: "350px" }} spacing={1}>
                  <Typography fontWeight="bold">
                    Template Type: Kronologic Event
                  </Typography>
                  <Typography>
                    <Typography fontWeight="bold" component="span">
                      Event Location:
                    </Typography>{" "}
                    {/*TODO: maybe also use same check for non-webinar meeting details*/}
                    {data?.location ? (
                      isMeetingLink(data.location) ? (
                        <a
                          href={data.location}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          Link to meeting
                        </a>
                      ) : (
                        data.location
                      )
                    ) : (
                      "None"
                    )}
                  </Typography>
                </Stack>
              </Stack>
            </Stack>

            <Stack direction="row" spacing={1}>
              <Button
                onClick={() => setShowAddGuestDialog(true)}
                startIcon={<PersonAdd />}
                disabled={isAddGuestDisabled}
                variant="contained"
                sx={{
                  backgroundColor: "#6b7280",
                  color: "white",
                  textTransform: "none",
                  "&:hover": {
                    backgroundColor: "#4b5563",
                  },
                  "&:disabled": {
                    backgroundColor: "#e0e0e0",
                    color: "#9e9e9e",
                  },
                }}
              >
                Add Guest
              </Button>

              <Button
                onClick={activateWebinar}
                disabled={isActivateDisabled}
                startIcon={<PlayArrow />}
                variant="contained"
                sx={{
                  backgroundColor: "#6b7280",
                  color: "white",
                  textTransform: "none",
                  "&:hover": {
                    backgroundColor: "#4b5563",
                  },
                  "&:disabled": {
                    backgroundColor: "#e0e0e0",
                    color: "#9e9e9e",
                  },
                }}
              >
                Activate Event
              </Button>
            </Stack>
            <Grid container spacing={2}>
              <Grid item xs={9}>
                <Stack
                  sx={{
                    borderStyle: "solid",
                    borderRadius: "5px",
                    borderColor: "black",
                    borderWidth: "1px",
                    width: "100%",
                    minHeight: "600px",
                    p: 2,
                    gap: 2,
                    overflow: "hidden",
                  }}
                >
                  <Tabs
                    value={tab}
                    variant="scrollable"
                    scrollButtons="auto"
                    sx={{
                      textDecoration: 0,
                      borderBottom: "1px solid #CDD6DD",
                      minHeight: "48px",
                      "& .MuiTab-root": {
                        minWidth: "auto",
                        padding: "6px 16px",
                      },
                    }}
                    TabIndicatorProps={{
                      style: { backgroundColor: "transparent" },
                    }}
                    onChange={(_event, value: EventDetailsPageTabs) => {
                      switch (value) {
                        case "Everyone":
                          setGuestStatusFilter("");
                          break;
                        case "Accepted":
                          setGuestStatusFilter("accepted");
                          break;
                        case "Declined":
                          setGuestStatusFilter("declined");
                          break;
                        case "No Response":
                          setGuestStatusFilter("no_response");
                          break;
                        case "Awaiting Response":
                          setGuestStatusFilter("waiting_for_first_response");
                          break;
                      }
                      setTab(value);
                    }}
                  >
                    <Tab
                      label={`Everyone (${guestCounts.data?.total ?? 0})`}
                      value="Everyone"
                      sx={{ textTransform: "none" }}
                    />
                    <Tab
                      label={`Accepted (${guestCounts.data?.acceptedTotal ?? 0})`}
                      value="Accepted"
                      sx={{ textTransform: "none" }}
                    />{" "}
                    <Tab
                      label={`Declined (${guestCounts.data?.declinedTotal ?? 0})`}
                      value="Declined"
                      sx={{ textTransform: "none" }}
                    />
                    <Tab
                      label={`No Response (${guestCounts.data?.noResponseTotal ?? 0})`}
                      value="No Response"
                      sx={{ textTransform: "none" }}
                    />
                    <Tab
                      label={`Awaiting Response (${guestCounts.data?.awaitingResponseTotal ?? 0})`}
                      value="Awaiting Response"
                      sx={{ textTransform: "none" }}
                    />
                  </Tabs>
                  {data.guests && data.guests.length > 0 && (
                    <SelectTable
                      loading={loading}
                      data={data.guests}
                      selected={selectedGuestIds}
                      onSelect={handleGuestSelection}
                      getDisabled={(guest) => !!guest.followUpTemplate}
                      columns={[
                        {
                          id: "guestInfo",
                          label: "Guest",
                          component: (row: Guest) => (
                            <Stack>
                              <Typography variant="caption">
                                {row.firstName} {row.lastName}
                              </Typography>
                              <Typography variant="caption">
                                {row.email}
                              </Typography>
                            </Stack>
                          ),
                        },
                        {
                          id: "status",
                          label: "Event Guest Status",
                          component: (row: Guest) => (
                            <GuestStatusIndicator status={row.status} />
                          ),
                        },
                        {
                          id: "followUpTemplate",
                          label: "Follow Up Template",
                          component: (row: Guest) => (
                            <Stack sx={{ minWidth: 200 }}>
                              {editingTemplateGuestId === row.id ? (
                                <FollowUpTemplateDropdown
                                  value={
                                    row.followUpTemplate as SkinnyMeetingDefinition
                                  }
                                  onChange={(template) =>
                                    handleTemplateAssignment(row.id, template)
                                  }
                                  onCreateNewTemplate={handleCreateNewTemplate}
                                  refreshTrigger={refreshTrigger}
                                />
                              ) : (
                                <Stack
                                  onClick={() =>
                                    setEditingTemplateGuestId(row.id)
                                  }
                                  sx={{
                                    cursor: "pointer",
                                    "&:hover": {
                                      backgroundColor: "action.hover",
                                    },
                                    p: 1,
                                    borderRadius: 1,
                                  }}
                                >
                                  {row.followUpTemplate ? (
                                    <Stack
                                      direction="row"
                                      alignItems="left"
                                    >
                                      {getTemplateIcon(
                                        row.followUpTemplate.inviteStyle,
                                      )}
                                      <Typography
                                        variant="caption"
                                        fontWeight="bold"
                                      >
                                        {row.followUpTemplate.name}
                                      </Typography>
                                    </Stack>
                                  ) : (
                                    <Typography
                                      variant="caption"
                                      color="text.secondary"
                                    >
                                      Click to assign template
                                    </Typography>
                                  )}
                                </Stack>
                              )}
                            </Stack>
                          ),
                        },
                        {
                          id: "assignedHost",
                          label: "Assigned Host",
                          component: (row: Guest) => (
                            <Stack sx={{ minWidth: 150 }}>
                              {editingHostGuestId === row.id ? (
                                <HostSelectInput
                                  host={null}
                                  onHostSelect={(host) => {
                                    if (host) {
                                      handleAssignHost(row.id, host.id);
                                    } else {
                                      setEditingHostGuestId(null);
                                    }
                                  }}
                                  forMeetingTransfer={true}
                                />
                              ) : (
                                <Stack
                                  onClick={() => {
                                    if (row.followUpTemplate) {
                                      setEditingHostGuestId(row.id);
                                    }
                                  }}
                                  sx={{
                                    cursor: row.followUpTemplate
                                      ? "pointer"
                                      : "default",
                                    "&:hover": row.followUpTemplate
                                      ? { backgroundColor: "action.hover" }
                                      : {},
                                    p: 1,
                                    borderRadius: 1,
                                    opacity: row.followUpTemplate ? 1 : 0.6,
                                  }}
                                >
                                  <Typography variant="caption">
                                    {row.assignedHost
                                      ? `${row.assignedHost.firstName} ${row.assignedHost.lastName}`
                                      : row.followUpTemplate
                                        ? "Click to assign host"
                                        : "Template required"}
                                  </Typography>
                                </Stack>
                              )}
                            </Stack>
                          ),
                        },
                        {
                          id: "followUpStatus",
                          label: "Follow Up Status",
                          component: (row: Guest) => (
                            <Chip
                              size="small"
                              label={
                                row.followUpStatus === "ready"
                                  ? "Ready"
                                  : row.followUpStatus === "staging"
                                    ? "Staging"
                                    : row.followUpStatus === "sent"
                                      ? "Sent"
                                      : row.followUpStatus === "needs_host"
                                        ? "Needs Host"
                                        : row.followUpStatus ===
                                            "needs_template"
                                          ? "Needs Template"
                                          : "None"
                              }
                              color={
                                row.followUpStatus === "sent"
                                  ? "success"
                                  : row.followUpStatus === "ready"
                                    ? "info"
                                    : row.followUpStatus === "staging"
                                      ? "warning"
                                      : row.followUpStatus === "needs_host" ||
                                          row.followUpStatus ===
                                            "needs_template"
                                        ? "error"
                                        : "default"
                              }
                            />
                          ),
                        },
                      ]}
                    />
                  )}
                </Stack>
              </Grid>
              <Grid item xs={3} sx={{ width: "200px" }}>
                <Stack spacing={2} sx={{ p: 2 }}>
                  <FollowUpButton
                    eventId={Number.parseInt(id)}
                    selectedGuests={
                      hasAssignedTemplates
                        ? selectedGuests
                        : selectedGuestsWithoutTemplates
                    }
                    onTemplateAssign={handleTemplateAssign}
                    onBeginFollowUp={handleBeginFollowUp}
                    hasAssignedTemplates={hasAssignedTemplates}
                    disabled={loading}
                  />
                </Stack>
              </Grid>
            </Grid>
          </Stack>
        </Stack>
      </Stack>

      <AddEventGuestDialog
        open={showAddGuestDialog}
        onClose={() => setShowAddGuestDialog(false)}
        onConfirm={handleAddEventGuest}
      />

      <CreateTemplateModal
        open={showCreateTemplateModal}
        onClose={() => setShowCreateTemplateModal(false)}
        onTemplateCreated={handleTemplateCreated}
      />
    </Container>
  );
}

export default EventDetails;
