import React, { useState } from "react";
import { Stack, Typography } from "@mui/material";
import { HostSelectInput, MeetingHost } from "../inputs";
import PrimaryButton from "../buttons/PrimaryButton";
import SecondaryButton from "../buttons/SecondaryButton";
import Dialog from "./Dialog";

interface CurrentHost {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
}

export function ChangeHostDialog({
  open,
  onClose,
  onConfirm,
  currentHost,
}: {
  open: boolean;
  onClose?: () => void;
  onConfirm?: (hostId: number) => void;
  currentHost: CurrentHost | null;
}) {
  const [selectedHost, setSelectedHost] = useState<MeetingHost | null>(null);

  const handleConfirm = () => {
    if (onConfirm && selectedHost) {
      onConfirm(selectedHost.id);
    }
  };

  const handleClose = () => {
    setSelectedHost(null);
    if (onClose) {
      onClose();
    }
  };

  return (
    <Dialog
      title="Change Event Host"
      open={open}
      onClose={handleClose}
      actions={
        <>
          <PrimaryButton
            onClick={handleConfirm}
            disabled={!selectedHost || selectedHost.id === currentHost?.id}
          >
            Change Host
          </PrimaryButton>
          <SecondaryButton onClick={handleClose}>Cancel</SecondaryButton>
        </>
      }
    >
      <Stack spacing={2} sx={{ pt: 1, minHeight: "15rem", minWidth: "400px" }}>
        {currentHost && (
          <Stack spacing={1}>
            <Typography variant="body2" color="text.secondary">
              Current Host:
            </Typography>
            <Typography variant="body1">
              {currentHost.firstName} {currentHost.lastName} ({currentHost.email})
            </Typography>
          </Stack>
        )}
        
        <Stack spacing={1}>
          <Typography variant="body2" color="text.secondary">
            Select New Host:
          </Typography>
          <HostSelectInput
            host={selectedHost}
            onHostSelect={setSelectedHost}
            forMeetingTransfer={true}
          />
        </Stack>
        
        {selectedHost && selectedHost.id === currentHost?.id && (
          <Typography variant="body2" color="warning.main">
            Please select a different host than the current one.
          </Typography>
        )}
      </Stack>
    </Dialog>
  );
}
