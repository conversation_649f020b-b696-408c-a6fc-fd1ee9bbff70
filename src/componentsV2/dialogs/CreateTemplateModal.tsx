import React, { useState, useEffect } from "react";
import {
  Box,
  Step,
  StepButton,
  Stepper,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { Error as ErrorIcon } from "@mui/icons-material";
import { FormProvider, useForm } from "react-hook-form";
import Dialog from "./Dialog";
import PrimaryButton from "../buttons/PrimaryButton";
import SecondaryButton from "../buttons/SecondaryButton";
import { TemplateEditSettings } from "../TemplateEditSettings";
import { TemplateEditInviteAndEmail } from "../TemplateEditInviteAndEmail";
import { TemplateEditDistribution } from "../TemplateEditDistribution";
import { TemplateEditTimeAttributes } from "../TemplateEditTimeAttributes";
import { useCreateMeetingTemplate } from "src/mutations/useCreateMeetingTemplate";
import {
  MeetingTemplateUpdateBody,
  useUpdateMeetingTemplate,
} from "src/mutations/useUpdateMeetingTemplate";
import { useUpdateRoutingLogicUser } from "src/mutations/useUpdateRoutingLogicUser";
import { useMeetingDefinition, SkinnyMeetingDefinition } from "src/queries";
import { useRoutingLogic, RoutingLogic } from "src/queries/useRoutingLogic";
import { MeetingDefinition } from "src/types";

import useGeneralNotifications from "src/hooks/useGeneralNotifications";
import { useIsDefaultUser, useActingAsOverrideHeader } from "src/auth";
import { useTokenRefreshHandler } from "src/hooks";
import { useUserService } from "src/services";
import { getUserToken } from "src/utils/jwtToken";
import { errorHandler } from "src/hooks/errorHandler";

interface CreateTemplateModalProps {
  open: boolean;
  onClose: () => void;
  onTemplateCreated: (template: SkinnyMeetingDefinition) => void;
}

export function CreateTemplateModal({
  open,
  onClose,
  onTemplateCreated,
}: CreateTemplateModalProps) {
  const [activeStep, setActiveStep] = useState(0);
  const [createdTemplateId, setCreatedTemplateId] = useState<number | null>(
    null,
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hideMeetingHosts, setHideMeetingHosts] = useState(false);

  const { addGeneralNotification, addError } = useGeneralNotifications();
  const createMeetingTemplate = useCreateMeetingTemplate();
  const updateMeetingTemplate = useUpdateMeetingTemplate();
  const updateRoutingLogicUser = useUpdateRoutingLogicUser();
  const isDefaultUser = useIsDefaultUser();

  const methods = useForm<MeetingDefinition>({
    mode: "all",
    defaultValues: {
      name: "",
      inviteStyle: "custom",
    },
  });

  const {
    handleSubmit,
    register,
    reset,
    getValues,
    setValue,
    trigger,
    formState: { errors },
  } = methods;

  // Fetch template data and routing logic when template is created
  const { data: meetingDefinition } = useMeetingDefinition(
    createdTemplateId ? createdTemplateId.toString() : undefined,
  );
  const { data: routingLogic } = useRoutingLogic(
    createdTemplateId ? createdTemplateId.toString() : undefined,
  );

  // ---------------- Default routing logic fetching.
  // We have to do all of this here, because we need to invoke this API call on team change, and wait for the result.
  const accessToken = getUserToken();
  const service = useUserService();
  const tokenRefreshHandler = useTokenRefreshHandler();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  const fetchDefaultRoutingLogic = (teamId: number | string) =>
    service
      .get(`/api/meetings/definition/default_routing_logic/${teamId}`)
      .set(headers)
      .then(tokenRefreshHandler)
      .then((res: Response) => res.body)
      .then((data?: RoutingLogic[]) => data)
      .catch(errorHandler);
  // ---------------- Default routing logic fetching END.

  // Reset form when modal opens
  useEffect(() => {
    if (open) {
      reset({
        name: "",
        inviteStyle: "custom",
      });
    }
  }, [open, reset]);

  // Selectively populate form with backend data while preserving user selections
  useEffect(() => {
    if (meetingDefinition && createdTemplateId) {
      const currentFormValues = getValues();
      reset({
        ...meetingDefinition,
        // Preserve user-entered values
        name: currentFormValues.name || meetingDefinition.name,
        inviteStyle:
          currentFormValues.inviteStyle || meetingDefinition.inviteStyle,
      });
    }
  }, [meetingDefinition, createdTemplateId, reset, getValues]);

  const handleClose = () => {
    setActiveStep(0);
    setCreatedTemplateId(null);
    setIsSubmitting(false);
    setHideMeetingHosts(false);
    reset();
    onClose();
  };

  const handleNext = () => {
    if (activeStep < 3) {
      setActiveStep(activeStep + 1);
    }
  };

  const handlePrevious = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    }
  };

  const handleStepClick = (step: number) => {
    setActiveStep(step);
  };

  // Create initial template on first step completion
  const handleCreateTemplate = async (_: MeetingDefinition) => {
    if (createdTemplateId) return; // Already created

    try {
      setIsSubmitting(true);
      const newTemplate = await createMeetingTemplate();
      setCreatedTemplateId(newTemplate.id);
      addGeneralNotification("Template created successfully!");
    } catch (error) {
      addError(`Failed to create template: ${error}`);
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatRoutingLogic = (
    templateUpdateBody: MeetingTemplateUpdateBody,
    formData: MeetingDefinition,
    newRoutingLogic?: RoutingLogic[],
    routingLogic?: RoutingLogic[],
  ) => {
    // We have to keep the code below for now until we get a new endpoint for updating Meeting Templates.
    // If the team has changed, newRoutingLogic is passed in as a third param, and we need to use it to reset the sequential props, in the routing_logic prop.
    if (newRoutingLogic) {
      // Convert email routing to custom when using newRoutingLogic
      if (formData.routing === "email") {
        templateUpdateBody.routing = "custom";

        // Set metadata for email routing
        if (templateUpdateBody.routing_logic) {
          const subtypeDetailsField =
            templateUpdateBody.routing_logic.metadata?.subtypeDetails.field ??
            "routing_field";

          templateUpdateBody.routing_logic.metadata = {
            subtypeDetails: {
              field: subtypeDetailsField,
              subtype: "email",
            },
          };
        }
      } else {
        delete templateUpdateBody.routing_logic?.metadata;
      }

      if (templateUpdateBody.routing_logic) {
        templateUpdateBody.routing_logic.sequential.order = newRoutingLogic.map(
          (routingLogicItem) => routingLogicItem.user_id,
        );

        templateUpdateBody.routing_logic.custom = {};

        if (formData.routing === "email") {
          // For email routing, create mapping_logic with email values
          newRoutingLogic.forEach((routingLogicItem) => {
            if (templateUpdateBody.routing_logic) {
              const subtypeDetailsField =
                templateUpdateBody.routing_logic.metadata?.subtypeDetails
                  .field ?? "routing_field";

              templateUpdateBody.routing_logic.custom[
                routingLogicItem.user_id
              ] = {
                enabled: routingLogicItem.enabled,
                mapping_logic: [
                  {
                    field: subtypeDetailsField,
                    operator: "is",
                    value: routingLogicItem.email,
                  },
                ],
              };
            }
          });
        } else {
          // For sequential routing, just set enabled flag
          newRoutingLogic.forEach((routingLogicItem) => {
            if (templateUpdateBody.routing_logic) {
              templateUpdateBody.routing_logic.custom[
                routingLogicItem.user_id
              ] = {
                enabled: routingLogicItem.enabled,
                mapping_logic: routingLogicItem.mapping_logic,
              };
            }
          });
        }
      }

      // If newRoutingLogic isn't passed in, we need to populate the routing_logic properties with the data from the
      // routing logic endpoint. We populate these differently, depending on if the user selected the "email" or "sequential" routing logic.
    } else if (formData.routing === "email") {
      if (templateUpdateBody.routing_logic) {
        const subtypeDetailsField =
          templateUpdateBody.routing_logic.metadata?.subtypeDetails.field ??
          "routing_field";

        templateUpdateBody.routing = "custom";
        templateUpdateBody.routing_logic.metadata = {
          subtypeDetails: {
            field: subtypeDetailsField,
            subtype: "email",
          },
        };

        routingLogic?.forEach((item) => {
          if (templateUpdateBody.routing_logic) {
            templateUpdateBody.routing_logic.custom[item.user_id] = {
              enabled: item.enabled,
              mapping_logic: [
                {
                  field: subtypeDetailsField,
                  operator: "is",
                  value: item.email,
                },
              ],
            };
          }
        });
      }
    } else if (formData.routing === "sequential") {
      delete templateUpdateBody.routing_logic?.metadata;

      // For both the sequential and custom routing logic we have to set the `enabled` flag.
      // We do not touch the sequential property, just this one in both cases.
      routingLogic?.forEach((item) => {
        if (templateUpdateBody.routing_logic) {
          templateUpdateBody.routing_logic.custom[item.user_id] = {
            enabled: item.enabled,
            mapping_logic: [],
          };
        }
      });
    }

    // We have to delete this because otherwise the patch fails.
    delete templateUpdateBody.routing_logic?.sequential.last_processed;

    return templateUpdateBody.routing_logic;
  };

  // Save template data
  const onSubmit = async (
    data: MeetingDefinition,
    _?: React.BaseSyntheticEvent,
    newRoutingLogic?: RoutingLogic[],
  ) => {
    if (!createdTemplateId) return;

    try {
      setIsSubmitting(true);
      const templateUpdateBody = {
        ...data,
        enabled: data.active,
        team: data.team.id,
        routingJustMe: data.routingJustMe,
        tags: data.tags.map((tag) => tag.id),
      };

      if (!isDefaultUser) {
        // Use newRoutingLogic if provided (from team change), otherwise use existing routingLogic
        const routingLogicToUse = newRoutingLogic || routingLogic;

        if (routingLogicToUse && routingLogicToUse.length > 0) {
          templateUpdateBody.routing_logic = formatRoutingLogic(
            templateUpdateBody,
            data,
            routingLogicToUse,
            routingLogic,
          );
        } else {
          // Default routing logic for team templates without routing data
          templateUpdateBody.routing = "sequential";
          templateUpdateBody.routing_logic = {
            sequential: {
              last_processed: 0,
              order: [],
            },
            custom: {},
          };
        }
      } else {
        templateUpdateBody.routing = "sequential";
        templateUpdateBody.routing_logic = {
          sequential: {
            last_processed: 0,
            order: [],
          },
          custom: {},
        };
      }

      if (data.inviteStyle === "link_first") {
        templateUpdateBody.properties.dayRange.from = 1;
        templateUpdateBody.properties.dayRange.to = 30;
      }

      await updateMeetingTemplate(createdTemplateId, templateUpdateBody);

      // Create the skinny template object for the callback
      // Use the meetingDefinition data if available, otherwise use form data
      const skinnyTemplate: SkinnyMeetingDefinition = {
        id: createdTemplateId,
        name: data.name,
        inviteStyle: data.inviteStyle,
        active: data.active || true,
        creationData: meetingDefinition?.creationData || {
          creatorData: {
            userId: 0,
            userEmail: "",
            userFirstName: "",
            userLastName: "",
            userRole: 0,
            adminEmail: "",
            adminId: 0,
          },
          createdAt: new Date().toISOString(),
        },
        schedulingUrls: meetingDefinition?.schedulingUrls || null,
      };

      onTemplateCreated(skinnyTemplate);
      addGeneralNotification("Template saved successfully!");
      handleClose();
    } catch (error) {
      addError(`Failed to save template: ${error}`);
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNextWithSave = async () => {
    if (activeStep === 0) {
      const isNameValid = await trigger("name");
      if (!isNameValid) {
        return;
      }

      if (!createdTemplateId) {
        await handleSubmit(handleCreateTemplate)();
      }
    }
    handleNext();
  };

  const handleFinalSubmit = async () => {
    await handleSubmit(onSubmit)();
  };

  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return <TemplateEditSettings isForEventFollowupModal={true} />;
      case 1:
        return <TemplateEditInviteAndEmail />;
      case 2:
        return (
          <TemplateEditDistribution
            hideMeetingHosts={hideMeetingHosts}
            currentTeam={meetingDefinition?.team}
            routingLogic={routingLogic}
            createdByDefaultUser={false}
            onTeamChange={async () => {
              if (!createdTemplateId) return;

              try {
                setHideMeetingHosts(false);
                const newRoutingLogic = await fetchDefaultRoutingLogic(
                  getValues("team.id"),
                );

                // When changing teams, always default to the "sequential" routing logic first.
                setValue("routing", "sequential");

                const data = getValues();
                const templateUpdateBody = {
                  ...data,
                  enabled: data.active,
                  team: data.team.id,
                  routingJustMe: data.routingJustMe,
                  tags: data.tags.map((tag) => tag.id),
                };

                if (!isDefaultUser) {
                  if (newRoutingLogic && newRoutingLogic.length > 0) {
                    templateUpdateBody.routing_logic = formatRoutingLogic(
                      templateUpdateBody,
                      data,
                      newRoutingLogic,
                      routingLogic,
                    );
                  } else {
                    templateUpdateBody.routing = "sequential";
                    templateUpdateBody.routing_logic = {
                      sequential: {
                        last_processed: 0,
                        order: [],
                      },
                      custom: {},
                    };
                  }
                } else {
                  templateUpdateBody.routing = "sequential";
                  templateUpdateBody.routing_logic = {
                    sequential: {
                      last_processed: 0,
                      order: [],
                    },
                    custom: {},
                  };
                }

                if (data.inviteStyle === "link_first") {
                  templateUpdateBody.properties.dayRange.from = 1;
                  templateUpdateBody.properties.dayRange.to = 30;
                }

                await updateMeetingTemplate(
                  createdTemplateId,
                  templateUpdateBody,
                );
              } catch (error: any) {
                if (error?.status === 404 && error.message) {
                  addError(JSON.parse(error.message).message);
                  setHideMeetingHosts(true);
                }
              }
            }}
            onUpdateRoutingLogicUser={async (routingLogic: RoutingLogic) => {
              if (createdTemplateId) {
                await updateRoutingLogicUser(
                  createdTemplateId,
                  routingLogic.user_id,
                  {
                    enabled: routingLogic.enabled,
                    mapping_logic: routingLogic.mapping_logic,
                  },
                );
              }
            }}
          />
        );
      case 3:
        return (
          <TemplateEditTimeAttributes
            meetingDefinitionId={createdTemplateId || undefined}
          />
        );
      default:
        return null;
    }
  };

  const getStepActions = () => {
    const isLastStep = activeStep === 3;
    const isFirstStep = activeStep === 0;

    return (
      <Stack direction="row" spacing={2} sx={{ mt: 3 }}>
        {!isFirstStep && (
          <SecondaryButton onClick={handlePrevious} disabled={isSubmitting}>
            Previous
          </SecondaryButton>
        )}
        {isLastStep ? (
          <PrimaryButton onClick={handleFinalSubmit} disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : "Submit"}
          </PrimaryButton>
        ) : (
          <PrimaryButton onClick={handleNextWithSave} disabled={isSubmitting}>
            {isSubmitting ? "Creating..." : "Next"}
          </PrimaryButton>
        )}
      </Stack>
    );
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      title="Create New Template"
      maxWidth="lg"
      minHeight="600px"
    >
      <FormProvider {...methods}>
        <Box sx={{ width: "100%", mb: 4 }}>
          {/* Template Name Field */}
          <Box sx={{ mb: 4 }}>
            <Stack>
              <TextField
                {...register("name", {
                  required: "Template name is required.",
                })}
                InputProps={{
                  sx: { fontWeight: "bold", fontSize: "22px" },
                }}
                sx={{ width: "400px" }}
                variant="standard"
                placeholder="New Template Name"
                label="Template name"
              />
              {errors.name && (
                <Typography sx={{ color: "red" }}>
                  {errors.name.message}
                </Typography>
              )}
            </Stack>
          </Box>

          {/* Stepper */}
          <Stepper nonLinear activeStep={activeStep} sx={{ mb: 4 }}>
            <Step>
              <StepButton onClick={() => handleStepClick(0)}>
                Template Settings
              </StepButton>
            </Step>
            <Step>
              <StepButton
                onClick={() => handleStepClick(1)}
                disabled={!createdTemplateId}
                icon={
                  (errors.emailTemplates ||
                    errors.inviteTemplates ||
                    errors.properties?.cleanDeclineRule ||
                    errors.properties?.meetingReminder) && (
                    <ErrorIcon sx={{ color: "red" }} />
                  )
                }
              >
                Email & Invite
              </StepButton>
            </Step>
            <Step>
              <StepButton
                onClick={() => handleStepClick(2)}
                disabled={!createdTemplateId}
              >
                Meeting Distribution
              </StepButton>
            </Step>
            <Step>
              <StepButton
                onClick={() => handleStepClick(3)}
                disabled={!createdTemplateId}
              >
                Time Attributes
              </StepButton>
            </Step>
          </Stepper>

          {/* Step Content */}
          <Box sx={{ minHeight: "400px" }}>{getStepContent(activeStep)}</Box>

          {/* Actions */}
          {getStepActions()}
        </Box>
      </FormProvider>
    </Dialog>
  );
}
